package com.example.canteenradio;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {
    private ImageView iv_horn;
    private TextView tv_left_content, tv_right_content;
    private MyBroadcastReceiver receiver;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        init();
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private void init() {
        iv_horn = findViewById(R.id.iv_horn);
        tv_left_content = findViewById(R.id.tv_left_content);
        tv_right_content = findViewById(R.id.tv_right_content);

        receiver = new MyBroadcastReceiver();
        String action = "Open_Rice";
        IntentFilter intentFilter = new IntentFilter(action);

        // 广播注册适配Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(receiver, intentFilter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(receiver, intentFilter);
        }

        iv_horn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                tv_right_content.setText("开饭啦！");

                // 创建广播并明确指定目标包名
                Intent intent = new Intent();
                intent.setAction("Open_Rice");
                intent.setPackage(getPackageName()); // 关键修复！

                sendBroadcast(intent);
            }
        });
    }

    // 优化广播接收器逻辑
    class MyBroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("Open_Rice".equals(intent.getAction())) {
                // UI操作需要在主线程执行
                runOnUiThread(() -> {
                    tv_left_content.setVisibility(View.VISIBLE);
                    Toast.makeText(MainActivity.this,
                            "接收到开饭信号", Toast.LENGTH_SHORT).show();
                });

                Log.i("MyBroadcastReceiver", "接收到开饭广播");
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unregisterReceiver(receiver);
    }
}